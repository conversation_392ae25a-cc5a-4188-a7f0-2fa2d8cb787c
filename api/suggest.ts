import { streamText } from 'ai';
import { google } from '@ai-sdk/google';
import type { VercelRequest, VercelResponse } from '@vercel/node';

const model = google('gemini-2.5-pro');

export default async function handler(req: VercelRequest, res: VercelResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { text } = req.body;

    const prompt = `
Help user keep writing; provide next prompt guides which could help user writing. Ask a direct question that prompts the user's next writing action based on their last text. Do not write new content; only ask or give a brief nudge. If no context is provided, ask what they are writing and their next goal. Always return one or two sentences;
the user's last text: ${text}
`;

    const result = await streamText({
      model,
      prompt,
    });

    // Convert the stream to a response
    const stream = result.toTextStreamResponse();
    
    // Set headers for streaming
    res.setHeader('Content-Type', 'text/plain; charset=utf-8');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    
    // Pipe the stream to the response
    if (stream.body) {
      const reader = stream.body.getReader();
      
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        res.write(new TextDecoder().decode(value));
      }
    }
    
    res.end();
  } catch (error: unknown) {
    console.error('Error in /api/suggest:', error);
    res.status(500).json({ 
      error: 'Internal Server Error', 
      message: error instanceof Error ? error.message : 'Unknown error' 
    });
  }
}
