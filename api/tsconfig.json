{"compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "module": "CommonJS", "moduleResolution": "node", "isolatedModules": true, "noEmit": true, "skipLibCheck": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true}, "include": ["*.ts"], "exclude": ["node_modules", "dist"]}