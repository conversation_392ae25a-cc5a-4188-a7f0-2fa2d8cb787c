import { streamText, convertToModelMessages, type UIMessage } from 'ai';
import { google } from '@ai-sdk/google';
import type { VercelRequest, VercelResponse } from '@vercel/node';

const model = google('gemini-2.5-pro');

export default async function handler(req: VercelRequest, res: VercelResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { messages }: { messages: Array<Omit<UIMessage, 'id'>> } = req.body;

    const result = await streamText({
      model,
      messages: convertToModelMessages(messages),
    });

    // Convert the stream to a response
    const stream = result.toUIMessageStreamResponse();
    
    // Set headers for streaming
    res.setHeader('Content-Type', 'text/plain; charset=utf-8');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    
    // Pipe the stream to the response
    if (stream.body) {
      const reader = stream.body.getReader();
      
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        res.write(new TextDecoder().decode(value));
      }
    }
    
    res.end();
  } catch (error: unknown) {
    console.error('Error in /api/chat:', error);
    res.status(500).json({ 
      error: 'Internal Server Error', 
      message: error instanceof Error ? error.message : 'Unknown error' 
    });
  }
}
